 // 返回顶部按钮
        const backToTopBtn = document.getElementById('backToTop');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.remove('opacity-0', 'invisible');
                backToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                backToTopBtn.classList.remove('opacity-100', 'visible');
                backToTopBtn.classList.add('opacity-0', 'invisible');
            }
        });
        
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 产品图片切换
        function changeMainImage(src) {
            const mainImage = document.getElementById('mainProductImage');
            mainImage.src = src;
            
            // 更新缩略图选中状态
            const thumbnails = document.querySelectorAll('.product-thumb');
            thumbnails.forEach(thumb => {
                if (thumb.querySelector('img').src === src) {
                    thumb.classList.add('active');
                } else {
                    thumb.classList.remove('active');
                }
            });
        }

        // 数量增减
        const decreaseBtn = document.getElementById('decreaseQuantity');
        const increaseBtn = document.getElementById('increaseQuantity');
        const quantityInput = document.getElementById('productQuantity');
        
        decreaseBtn.addEventListener('click', () => {
            const currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
            }
        });
        
        increaseBtn.addEventListener('click', () => {
            const currentValue = parseInt(quantityInput.value);
            if (currentValue < 100) {
                quantityInput.value = currentValue + 1;
            }
        });

        // 加入购物车按钮效果
        const addToCartBtn = document.querySelector('.bg-primary');
        addToCartBtn.addEventListener('click', (e) => {
            e.preventDefault();
            // 这里可以添加加入购物车的动画效果和逻辑
            alert('商品已加入购物车！');
        });