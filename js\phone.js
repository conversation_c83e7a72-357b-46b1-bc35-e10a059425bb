 // 返回顶部按钮
        const backToTopBtn = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.remove('opacity-0', 'invisible');
                backToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                backToTopBtn.classList.remove('opacity-100', 'visible');
                backToTopBtn.classList.add('opacity-0', 'invisible');
            }
        });

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 产品图片切换
        function changeMainImage(src) {
            const mainImage = document.getElementById('mainProductImage');
            mainImage.src = src;

            // 更新放大镜中的图片
            const magnifiedImg = document.querySelector('.magnified-img img');
            if (magnifiedImg) {
                magnifiedImg.src = src;
            }

            // 更新缩略图选中状态
            const thumbnails = document.querySelectorAll('.product-thumb');
            thumbnails.forEach(thumb => {
                if (thumb.querySelector('img').src === src) {
                    thumb.classList.add('active');
                } else {
                    thumb.classList.remove('active');
                }
            });
        }

        // 放大镜功能
        function initMagnifier() {
            const productMainImg = document.querySelector('.product-main-img');
            if (!productMainImg) return;

            const img = productMainImg.querySelector('img');
            const lens = document.querySelector('.magnifier-lens');
            const magnifiedImg = document.querySelector('.magnified-img');
            const magnifiedImgInner = magnifiedImg.querySelector('img');

            // 设置放大倍数
            const ratio = 2.5;

            // 初始化放大镜
            function initLens() {
                // 复制原图到放大区域
                magnifiedImgInner.src = img.src;

                // 计算放大镜尺寸
                const lensWidth = magnifiedImg.offsetWidth / ratio;
                const lensHeight = magnifiedImg.offsetHeight / ratio;

                lens.style.width = `${lensWidth}px`;
                lens.style.height = `${lensHeight}px`;
            }

            // 更新放大镜位置和放大效果
            function updateMagnifier(e) {
                // 获取鼠标相对于图片的位置
                const rect = img.getBoundingClientRect();
                let mouseX = e.clientX - rect.left;
                let mouseY = e.clientY - rect.top;

                // 计算放大镜位置
                const lensWidth = lens.offsetWidth;
                const lensHeight = lens.offsetHeight;

                // 限制放大镜在图片内
                if (mouseX < lensWidth / 2) mouseX = lensWidth / 2;
                if (mouseX > rect.width - lensWidth / 2) mouseX = rect.width - lensWidth / 2;
                if (mouseY < lensHeight / 2) mouseY = lensHeight / 2;
                if (mouseY > rect.height - lensHeight / 2) mouseY = rect.height - lensHeight / 2;

                // 设置放大镜位置
                lens.style.left = `${mouseX - lensWidth / 2}px`;
                lens.style.top = `${mouseY - lensHeight / 2}px`;

                // 计算放大图片的位置
                const magnifyX = (mouseX / rect.width) * 100;
                const magnifyY = (mouseY / rect.height) * 100;

                magnifiedImgInner.style.width = `${img.width * ratio}px`;
                magnifiedImgInner.style.height = `${img.height * ratio}px`;
                magnifiedImgInner.style.left = `${-magnifyX * (magnifiedImgInner.width / 100) + magnifiedImg.offsetWidth / 2}px`;
                magnifiedImgInner.style.top = `${-magnifyY * (magnifiedImgInner.height / 100) + magnifiedImg.offsetHeight / 2}px`;
            }

            // 绑定事件
            productMainImg.addEventListener('mouseenter', () => {
                initLens();
                lens.style.display = 'block';
                magnifiedImg.style.display = 'block';
            });

            productMainImg.addEventListener('mouseleave', () => {
                lens.style.display = 'none';
                magnifiedImg.style.display = 'none';
            });

            productMainImg.addEventListener('mousemove', updateMagnifier);
        }

        // 初始化放大镜
        initMagnifier();

        // 数量增减
        const decreaseBtn = document.getElementById('decreaseQuantity');
        const increaseBtn = document.getElementById('increaseQuantity');
        const quantityInput = document.getElementById('productQuantity');

        decreaseBtn.addEventListener('click', () => {
            const currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
            }
        });

        increaseBtn.addEventListener('click', () => {
            const currentValue = parseInt(quantityInput.value);
            if (currentValue < 100) {
                quantityInput.value = currentValue + 1;
            }
        });

        // 加入购物车按钮效果
        const addToCartBtn = document.querySelector('.bg-primary');
        addToCartBtn.addEventListener('click', (e) => {
            e.preventDefault();
            // 这里可以添加加入购物车的动画效果和逻辑
            alert('商品已加入购物车！');
        });