<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leng-shopping - 2025新款高性能笔记本电脑</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#78a4d4', 
                        secondary: '#FFB800',
                        dark: '#333333',
                        light: '#F5F5F5',
                        gray: '#999999'
                    },
                    fontFamily: {
                        sans: ['PingFang SC', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .hover-scale {
                transition: transform 0.3s ease;
            }
            .hover-scale:hover {
                transform: scale(1.03);
            }
            .product-thumb {
                border: 2px solid transparent;
            }
            .product-thumb.active {
                border-color: #FF4400;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 顶部通知栏 -->
    <div class="bg-primary/10 text-primary text-center py-1 text-sm">
        <p>限时优惠：全场电子产品满1000减100，点击查看详情 ></p>
    </div>

    <!-- 导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="text-2xl font-bold text-primary">Leng-shopping</a>
                </div>

                <!-- 搜索框 -->
                <div class="flex-1 max-w-xl mx-8">
                    <div class="relative">
                        <input type="text" placeholder="搜索电子产品..." 
                               class="w-full py-2 px-4 pr-10 rounded-full border-2 border-primary focus:outline-none focus:ring-2 focus:ring-primary/50">
                        <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white p-1.5 rounded-full">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 用户功能区 -->
                <div class="flex items-center space-x-4">
                    <a href="#" class="text-dark hover:text-primary transition-colors">
                        <i class="fa fa-user-o mr-1"></i> 登录
                    </a>
                    <a href="#" class="text-dark hover:text-primary transition-colors relative">
                        <i class="fa fa-shopping-cart mr-1"></i> 购物车
                        <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- 主导航 -->
        <nav class="bg-primary text-white">
            <div class="container mx-auto px-4">
                <ul class="flex items-center">
                    <li class="relative group">
                        <a href="#" class="block py-3 px-6 bg-primary/90 font-medium">
                            <i class="fa fa-th-large mr-2"></i>全部商品分类
                        </a>
                        <div class="absolute left-0 top-full w-64 bg-white shadow-lg hidden group-hover:block z-50">
                            <ul class="py-2">
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>手机</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>笔记本电脑</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>耳机</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>智能手表</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li><a href="index.html" class="block py-3 px-6 hover:bg-primary/90 transition-colors">首页</a></li>
                    <li><a href="dzcp1.html" class="block py-3 px-6 hover:bg-primary/90 transition-colors">电子产品</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">服装鞋帽</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">家居用品</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">美妆个护</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">食品生鲜</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-4 py-6">
        <!-- 面包屑导航 -->
        <div class="text-sm text-gray-500 mb-6">
            <a href="index.html" class="hover:text-primary">首页</a>
            <span class="mx-2">/</span>
            <a href="dzcp1.html" class="hover:text-primary">电子产品</a>
            <span class="mx-2">/</span>
            <a href="#" class="hover:text-primary">笔记本电脑</a>
            <span class="mx-2">/</span>
            <span class="text-dark">2025新款高性能笔记本电脑</span>
        </div>

        <!-- 产品详情 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- 产品图片 -->
                <div class="w-full lg:w-1/2">
                    <div class="relative mb-4">
                        <img id="mainProductImage" src="images/dzsc/dn.png" alt="2025新款高性能笔记本电脑" class="w-full h-auto rounded-lg shadow-sm">
                        <div class="absolute top-4 left-4">
                            <span class="bg-primary text-white text-xs px-2 py-1 rounded">热卖</span>
                            <span class="bg-secondary text-white text-xs px-2 py-1 rounded ml-2">新品</span>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3 overflow-x-auto scrollbar-hide pb-2">
                        <div class="product-thumb active cursor-pointer rounded" onclick="changeMainImage('images/dzsc/dn.png')">
                            <img src="images/dzsc/dn.png" alt="电脑正面" class="w-20 h-20 object-cover rounded">
                        </div>
                        <div class="product-thumb cursor-pointer rounded" onclick="changeMainImage('images/dzsc/dncm.jpg')">
                            <img src="images/dzsc/dncm.jpg" alt="电脑侧面" class="w-20 h-20 object-cover rounded">
                        </div>
                        <div class="product-thumb cursor-pointer rounded" onclick="changeMainImage('images/dzsc/dnbm.jpg')">
                            <img src="images/dzsc/dnbm.jpg" alt="电脑背面" class="w-20 h-20 object-cover rounded">
                        </div>
                        <div class="product-thumb cursor-pointer rounded" onclick="changeMainImage('images/dzsc/dnjp.jpg')">
                            <img src="images/dzsc/dnjp.jpg" alt="电脑键盘" class="w-20 h-20 object-cover rounded">
                        </div>
                        <div class="product-thumb cursor-pointer rounded" onclick="changeMainImage('images/dzsc/dnjk.jpg')">
                            <img src="images/dzsc/dnjk.jpg" alt="电脑接口" class="w-20 h-20 object-cover rounded">
                        </div>
                    </div>
                </div>

                <!-- 产品信息 -->
                <div class="w-full lg:w-1/2">
                    <h1 class="text-2xl font-bold text-dark mb-4">2025新款高性能笔记本电脑 16GB+1TB RTX4080</h1>
                    
                    <div class="flex items-center mb-4">
                        <div class="flex items-center text-yellow-400 text-sm">
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <i class="fa fa-star"></i>
                            <span class="text-gray-500 ml-1">4.9 (216条评价)</span>
                        </div>
                        <span class="text-gray-500 ml-4">月销 1.8万+</span>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg mb-6">
                        <div class="flex items-baseline mb-3">
                            <span class="text-gray-500 mr-2">价格</span>
                            <span class="text-primary text-3xl font-bold">¥7999</span>
                            <span class="text-gray-400 line-through text-sm ml-2">¥9999</span>
                            <span class="bg-primary/10 text-primary text-xs px-2 py-1 rounded ml-2">立省¥2000</span>
                        </div>
                        
                        <div class="flex items-center mb-3">
                            <span class="text-gray-500 w-16">促销</span>
                            <div>
                                <span class="bg-red-500/10 text-red-500 text-xs px-2 py-1 rounded">限时优惠：下单立减2000元</span>
                                <span class="bg-green-500/10 text-green-500 text-xs px-2 py-1 rounded ml-2">赠送价值999元电脑包+鼠标</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <span class="text-gray-500 w-16">服务</span>
                            <div class="text-gray-600 text-sm">
                                <span class="flex items-center inline-flex mr-4"><i class="fa fa-check-circle text-green-500 mr-1"></i> 正品保障</span>
                                <span class="flex items-center inline-flex mr-4"><i class="fa fa-check-circle text-green-500 mr-1"></i> 7天无理由退换</span>
                                <span class="flex items-center inline-flex"><i class="fa fa-check-circle text-green-500 mr-1"></i> 全国联保</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <div class="flex items-center mb-3">
                            <span class="text-gray-500 w-16">颜色</span>
                            <div class="flex flex-wrap gap-2">
                                <button class="px-4 py-2 border-2 border-primary text-dark bg-white rounded-md focus:outline-none">暗夜灰</button>
                                <button class="px-4 py-2 border border-gray-300 text-dark bg-white rounded-md hover:border-primary focus:outline-none">星辰银</button>
                            </div>
                        </div>
                        
                        <div class="flex items-center mb-3">
                            <span class="text-gray-500 w-16">配置</span>
                            <div class="flex flex-wrap gap-2">
                                <button class="px-4 py-2 border-2 border-primary text-dark bg-white rounded-md focus:outline-none">16GB+1TB+RTX4080</button>
                                <button class="px-4 py-2 border border-gray-300 text-dark bg-white rounded-md hover:border-primary focus:outline-none">32GB+2TB+RTX4090</button>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <span class="text-gray-500 w-16">数量</span>
                            <div class="flex items-center border border-gray-300 rounded-md">
                                <button class="w-10 h-10 flex items-center justify-center text-gray-500 hover:bg-gray-100" id="decreaseQuantity">-</button>
                                <input type="number" value="1" min="1" class="w-16 h-10 text-center border-x border-gray-300 focus:outline-none" id="productQuantity">
                                <button class="w-10 h-10 flex items-center justify-center text-gray-500 hover:bg-gray-100" id="increaseQuantity">+</button>
                            </div>
                            <span class="text-gray-500 ml-2">库存: 86件</span>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap gap-4">
                        <button class="px-8 py-3 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex-1 md:flex-none">
                            <i class="fa fa-shopping-cart mr-2"></i> 加入购物车
                        </button>
                        <button class="px-8 py-3 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex-1 md:flex-none">
                            <i class="fa fa-bolt mr-2"></i> 立即购买
                        </button>
                        <button class="w-12 h-12 flex items-center justify-center border border-gray-300 rounded-md text-gray-500 hover:border-primary hover:text-primary transition-colors">
                            <i class="fa fa-heart-o"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品特性 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
                    <i class="fa fa-microchip text-primary mr-2 text-xl"></i> 旗舰处理器
                </h3>
                <p class="text-gray-600">搭载最新一代旗舰处理器，性能提升40%，多任务处理更加流畅，大型游戏和专业软件轻松运行。</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
                    <i class="fa fa-picture-o text-primary mr-2 text-xl"></i> 4K超清屏幕
                </h3>
                <p class="text-gray-600">15.6英寸4K OLED屏幕，120Hz高刷新率，100% DCI-P3广色域，支持HDR10+显示技术，色彩更加鲜艳。</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-bold text-dark mb-4 flex items-center">
                    <i class="fa fa-battery-full text-primary mr-2 text-xl"></i> 长效续航
                </h3>
                <p class="text-gray-600">90Wh大容量电池，支持100W PD快充，45分钟即可充电至60%，让您告别电量焦虑。</p>
            </div>
        </div>

        <!-- 产品详情和评论选项卡 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
            <div class="border-b border-gray-200">
                <div class="flex">
                    <button class="px-6 py-4 text-primary font-medium border-b-2 border-primary">产品详情</button>
                    <button class="px-6 py-4 text-gray-500 font-medium hover:text-primary">用户评价 (216)</button>
                    <button class="px-6 py-4 text-gray-500 font-medium hover:text-primary">规格参数</button>
                    <button class="px-6 py-4 text-gray-500 font-medium hover:text-primary">售后服务</button>
                </div>
            </div>
            
            <div class="p-6">
                <div class="space-y-6">
                    <h3 class="text-xl font-bold text-dark">产品介绍</h3>
                    
                    <p class="text-gray-600">2025新款高性能笔记本电脑采用最新一代处理器和独立显卡，性能提升40%，同时功耗降低25%，为您带来更加流畅的使用体验。</p>
                    
                    <img src="images/dzsc/dnjk.jpg" alt="电脑展示" class="w-full h-auto rounded-lg shadow-sm">
                    
                    <h3 class="text-xl font-bold text-dark">屏幕</h3>
                    
                    <p class="text-gray-600">15.6英寸4K OLED屏幕，120Hz高刷新率，100% DCI-P3广色域，支持HDR10+显示技术，色彩更加鲜艳，画面更加流畅。</p>
                    
                    <img src="images/dzsc/dnpm.jpg" alt="屏幕展示" class="w-full h-auto rounded-lg shadow-sm">
                    
                    <h3 class="text-xl font-bold text-dark">性能与存储</h3>
                    
                    <p class="text-gray-600">搭载最新一代旗舰处理器，配合16GB超大运行内存和1TB高速存储，多任务处理更加流畅，大型游戏加载速度提升50%。</p>
                    
                    <img src="images/dzsc/dncm.jpg" alt="性能展示" class="w-full h-auto rounded-lg shadow-sm">
                    
                    <h3 class="text-xl font-bold text-dark">显卡与散热</h3>
                    
                    <p class="text-gray-600">NVIDIA RTX 4080独立显卡，支持光线追踪和DLSS 3技术，配合双风扇四铜管散热系统，确保高性能输出的同时保持低温稳定运行。</p>
                    
                    <img src="images/dzsc/xiank.jpg" alt="显卡与散热展示" class="w-full h-auto rounded-lg shadow-sm">
                    
                    <h3 class="text-xl font-bold text-dark">接口与外设</h3>
                    
                    <p class="text-gray-600">丰富的接口配置，包括Thunderbolt 4、USB-C 3.2 Gen 2、HDMI 2.1、SD卡槽等，支持外接多台显示器和高速外设。</p>
                    
                    <img src="images/dzsc/dnbm.jpg" alt="接口展示" class="w-full h-auto rounded-lg shadow-sm">
                </div>
            </div>
        </div>

        <!-- 推荐商品 -->
        <div class="mb-8">
            <h2 class="text-xl font-bold text-dark mb-6">相关推荐</h2>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                <!-- 推荐商品1 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="images/dzsc/dnb.jpg" alt="电脑包" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">专业防水电脑包 15.6英寸 防震耐磨</h3>
                        <p class="text-primary font-bold">¥299.0</p>
                    </div>
                </div>

                <!-- 推荐商品2-5 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="images/dzsc/shubiao.jpg" alt="无线鼠标" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">无线蓝牙鼠标 静音 超长续航</h3>
                        <p class="text-primary font-bold">¥129.0</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="images/dzsc/tzw.jpg" alt="扩展坞" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">多功能扩展坞 10合1 USB-C转HDMI</h3>
                        <p class="text-primary font-bold">¥349.0</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="images/dzsc/srdz.jpg" alt="散热底座" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">笔记本电脑散热底座 五风扇强力散热</h3>
                        <p class="text-primary font-bold">¥159.0</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="images/dzsc/jxjp.jpg" alt="机械键盘" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">机械键盘 青轴 RGB背光 全尺寸</h3>
                        <p class="text-primary font-bold">¥499.0</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-white mt-12">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-bold mb-4">Leng-shopping</h3>
                    <p class="text-gray-400 mb-4">提供优质电子产品，让生活更智能</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-weibo"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-wechat"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">购物指南</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">新手上路</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">常见问题</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">支付方式</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">配送说明</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">售后服务</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">售后政策</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">退款说明</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">维修服务</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">联系客服</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">关于我们</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">公司简介</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">加入我们</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">联系方式</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">隐私政策</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-500 text-sm">
                <p>© 2025 Leng-shopping 版权所有 | 营业执照 | 增值电信业务经营许可证</p>
            </div>
        </div>
    </footer>

    <!-- 购物车悬浮按钮 -->
    <a href="#" class="fixed bottom-6 right-6 bg-primary text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg hover:bg-primary/90 transition-colors">
        <i class="fa fa-shopping-cart"></i>
    </a>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="fixed bottom-6 right-24 bg-dark text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg hover:bg-dark/90 transition-colors opacity-0 invisible">
        <i class="fa fa-arrow-up"></i>
    </button>

    <script src="js/diannao.js"></script>
</body>
</html>    