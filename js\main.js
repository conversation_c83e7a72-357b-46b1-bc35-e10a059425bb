// 通用函数
function $(selector) {
    return document.querySelector(selector);
}

function $$(selector) {
    return document.querySelectorAll(selector);
}

// 表单验证
function validateForm(formId) {
    const form = $(formId);
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault(); // 阻止默认提交行为，改为手动处理

        let isValid = true;
        let firstInvalidField = null;

        // 验证用户名（只能是字母和数字）
        const username = form.querySelector('input[name="username"]');
        if (username) {
            const usernameRegex = /^[a-zA-Z0-9]+$/;
            if (!usernameRegex.test(username.value)) {
                showError(username, '用户名只能包含字母和数字');
                isValid = false;
                firstInvalidField = firstInvalidField || username;
            } else {
                clearError(username);
            }
        }

        // 验证密码（必须包含数字、字母和下划线）
        const password = form.querySelector('input[name="password"]');
        if (password) {
            const passwordRegex = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*_).+$/;
            if (!passwordRegex.test(password.value)) {
                showError(password, '密码必须包含数字、字母和下划线');
                isValid = false;
                firstInvalidField = firstInvalidField || password;
            } else {
                clearError(password);
            }
        }

        // 验证确认密码
        const confirmPassword = form.querySelector('input[name="confirm_password"]');
        if (confirmPassword && password) {
            if (confirmPassword.value !== password.value) {
                showError(confirmPassword, '两次输入的密码不一致');
                isValid = false;
                firstInvalidField = firstInvalidField || confirmPassword;
            } else {
                clearError(confirmPassword);
            }
        }

        // 验证电子邮箱
        const email = form.querySelector('input[name="email"]');
        if (email && email.value) {
            const emailRegex = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
            if (!emailRegex.test(email.value)) {
                showError(email, '请输入有效的电子邮箱地址');
                isValid = false;
                firstInvalidField = firstInvalidField || email;
            } else {
                clearError(email);
            }
        }

        // 验证手机号码
        const phone = form.querySelector('input[name="phone"]');
        if (phone && phone.value) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(phone.value)) {
                showError(phone, '请输入有效的11位手机号码');
                isValid = false;
                firstInvalidField = firstInvalidField || phone;
            } else {
                clearError(phone);
            }
        }

        // 如果表单无效，聚焦到第一个无效字段
        if (!isValid) {
            if (firstInvalidField) {
                firstInvalidField.focus();
            }
            return;
        }

        // 表单验证通过，处理表单提交
        if (formId === '#login-form') {
            handleLogin(form);
        } else if (formId === '#register-form') {
            handleRegister(form);
        }
    });
}

// 显示错误信息
function showError(input, message) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.add('error');
    const errorElement = formGroup.querySelector('.error-message');
    if (errorElement) {
        errorElement.textContent = message;
    }
}

// 清除错误信息
function clearError(input) {
    const formGroup = input.closest('.form-group');
    formGroup.classList.remove('error');
}

// 处理登录表单提交
function handleLogin(form) {
    const username = form.querySelector('input[name="username"]').value;
    const password = form.querySelector('input[name="password"]').value;

    // 在实际项目中，这里应该发送请求到服务器验证用户名和密码
    // 这里仅做前端模拟
    if (username === defaultAccount.username && password === defaultAccount.password) {
        // 登录成功
        saveUserInfo({
            username: username,
            email: defaultAccount.email,
            phone: defaultAccount.phone,
            loginTime: new Date().toISOString()
        });

        // 显示成功提示
        showLoginSuccessMessage();

        // 延迟跳转到个人中心
        setTimeout(() => {
            window.location.href = 'user_center.html';
        }, 1500);
    } else {
        // 登录失败
        alert('用户名或密码错误！');
    }
}

// 处理注册表单提交
function handleRegister(form) {
    const username = form.querySelector('input[name="username"]').value;
    const password = form.querySelector('input[name="password"]').value;
    const email = form.querySelector('input[name="email"]')?.value || '';
    const phone = form.querySelector('input[name="phone"]')?.value || '';

    // 在实际项目中，这里应该发送请求到服务器创建用户
    // 这里仅做前端模拟
    saveUserInfo({
        username: username,
        email: email,
        phone: phone,
        loginTime: new Date().toISOString()
    });

    // 显示成功提示
    alert('注册成功！即将跳转到个人中心...');

    // 延迟跳转到个人中心
    setTimeout(() => {
        window.location.href = 'user_center.html';
    }, 1500);
}

// 保存用户信息到本地存储
function saveUserInfo(userInfo) {
    localStorage.setItem('userInfo', JSON.stringify(userInfo));
}

// 显示登录成功消息
function showLoginSuccessMessage() {
    // 创建消息元素
    const successMsg = document.createElement('div');
    successMsg.className = 'login-success-message';
    successMsg.innerHTML = `
        <div class="success-icon">✓</div>
        <div class="success-text">登录成功！正在跳转到个人中心...</div>
    `;
    document.body.appendChild(successMsg);

    // 添加动画效果
    setTimeout(() => {
        successMsg.classList.add('show');
    }, 10);

    // 1.5秒后移除提示
    setTimeout(() => {
        successMsg.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(successMsg);
        }, 300);
    }, 1500);
}

// 添加实时表单验证
function addLiveValidation(formId) {
    const form = $(formId);
    if (!form) return;

    // 用户名实时验证
    const username = form.querySelector('input[name="username"]');
    if (username) {
        username.addEventListener('input', function() {
            const usernameRegex = /^[a-zA-Z0-9]+$/;
            if (this.value && !usernameRegex.test(this.value)) {
                showError(this, '用户名只能包含字母和数字');
            } else {
                clearError(this);
            }
        });
    }

    // 密码实时验证
    const password = form.querySelector('input[name="password"]');
    if (password) {
        password.addEventListener('input', function() {
            const passwordRegex = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*_).+$/;
            if (this.value && !passwordRegex.test(this.value)) {
                showError(this, '密码必须包含数字、字母和下划线');
            } else {
                clearError(this);
            }
        });
    }

    // 确认密码实时验证
    const confirmPassword = form.querySelector('input[name="confirm_password"]');
    if (confirmPassword && password) {
        confirmPassword.addEventListener('input', function() {
            if (this.value && this.value !== password.value) {
                showError(this, '两次输入的密码不一致');
            } else {
                clearError(this);
            }
        });
    }

    // 电子邮箱实时验证
    const email = form.querySelector('input[name="email"]');
    if (email) {
        email.addEventListener('input', function() {
            if (!this.value) {
                clearError(this);
                return;
            }
            const emailRegex = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
            if (!emailRegex.test(this.value)) {
                showError(this, '请输入有效的电子邮箱地址');
            } else {
                clearError(this);
            }
        });
    }

    // 手机号码实时验证
    const phone = form.querySelector('input[name="phone"]');
    if (phone) {
        phone.addEventListener('input', function() {
            if (!this.value) {
                clearError(this);
                return;
            }
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(this.value)) {
                showError(this, '请输入有效的11位手机号码');
            } else {
                clearError(this);
            }
        });
    }
}

// 轮播图功能
function initCarousel() {
    const carousel = $('.carousel');
    if (!carousel) return;

    const carouselInner = $('.carousel-inner');
    const items = $$('.carousel-item');
    const prevBtn = $('.carousel-prev');
    const nextBtn = $('.carousel-next');
    const indicators = $$('.carousel-indicator');

    let currentIndex = 0;
    let interval;
    const autoPlayDelay = 3000; // 自动播放间隔，单位毫秒

    // 触摸相关变量
    let touchStartX = 0;
    let touchEndX = 0;
    const minSwipeDistance = 50; // 最小滑动距离，单位像素

    // 设置轮播图宽度
    carouselInner.style.width = `${items.length * 100}%`;

    // 更新轮播图状态
    function updateCarousel() {
        carouselInner.style.transform = `translateX(-${currentIndex * (100 / items.length)}%)`;

        // 更新指示器状态
        indicators.forEach((indicator, index) => {
            if (index === currentIndex) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
    }

    // 切换到下一张图片
    function nextSlide() {
        currentIndex = (currentIndex + 1) % items.length;
        updateCarousel();
    }

    // 切换到上一张图片
    function prevSlide() {
        currentIndex = (currentIndex - 1 + items.length) % items.length;
        updateCarousel();
    }

    // 自动播放
    function startAutoPlay() {
        interval = setInterval(nextSlide, autoPlayDelay);
    }

    // 停止自动播放
    function stopAutoPlay() {
        clearInterval(interval);
    }

    // 绑定事件
    prevBtn.addEventListener('click', () => {
        prevSlide();
        stopAutoPlay();
        startAutoPlay();
    });

    nextBtn.addEventListener('click', () => {
        nextSlide();
        stopAutoPlay();
        startAutoPlay();
    });

    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            currentIndex = index;
            updateCarousel();
            stopAutoPlay();
            startAutoPlay();
        });
    });

    // 鼠标悬停时停止自动播放
    carousel.addEventListener('mouseenter', stopAutoPlay);
    carousel.addEventListener('mouseleave', startAutoPlay);

    // 触摸事件支持（移动端）
    carousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        stopAutoPlay();
    }, {passive: true});

    carousel.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
        startAutoPlay();
    }, {passive: true});

    // 处理滑动
    function handleSwipe() {
        const distance = touchEndX - touchStartX;
        if (distance < -minSwipeDistance) {
            // 向左滑动，显示下一张
            nextSlide();
        } else if (distance > minSwipeDistance) {
            // 向右滑动，显示上一张
            prevSlide();
        }
    }

    // 初始化
    updateCarousel();
    startAutoPlay();
}

// 放大镜功能
function initMagnifier() {
    const productMainImg = $('.product-main-img');
    if (!productMainImg) return;

    const img = productMainImg.querySelector('img');
    const lens = $('.magnifier-lens');
    const magnifiedImg = $('.magnified-img');
    const magnifiedImgInner = magnifiedImg.querySelector('img');

    // 设置放大倍数
    const ratio = 2.5;

    // 初始化放大镜
    function initLens() {
        // 复制原图到放大区域
        magnifiedImgInner.src = img.src;

        // 计算放大镜尺寸
        const lensWidth = magnifiedImg.offsetWidth / ratio;
        const lensHeight = magnifiedImg.offsetHeight / ratio;

        lens.style.width = `${lensWidth}px`;
        lens.style.height = `${lensHeight}px`;
    }

    // 更新放大镜位置和放大效果
    function updateMagnifier(e) {
        // 获取鼠标相对于图片的位置
        const rect = img.getBoundingClientRect();
        let mouseX = e.clientX - rect.left;
        let mouseY = e.clientY - rect.top;

        // 计算放大镜位置
        const lensWidth = lens.offsetWidth;
        const lensHeight = lens.offsetHeight;

        // 限制放大镜在图片内
        if (mouseX < lensWidth / 2) mouseX = lensWidth / 2;
        if (mouseX > rect.width - lensWidth / 2) mouseX = rect.width - lensWidth / 2;
        if (mouseY < lensHeight / 2) mouseY = lensHeight / 2;
        if (mouseY > rect.height - lensHeight / 2) mouseY = rect.height - lensHeight / 2;

        // 设置放大镜位置
        lens.style.left = `${mouseX - lensWidth / 2}px`;
        lens.style.top = `${mouseY - lensHeight / 2}px`;

        // 计算放大图片的位置
        const magnifyX = (mouseX / rect.width) * 100;
        const magnifyY = (mouseY / rect.height) * 100;

        magnifiedImgInner.style.width = `${img.width * ratio}px`;
        magnifiedImgInner.style.height = `${img.height * ratio}px`;
        magnifiedImgInner.style.left = `${-magnifyX * (magnifiedImgInner.width / 100) + magnifiedImg.offsetWidth / 2}px`;
        magnifiedImgInner.style.top = `${-magnifyY * (magnifiedImgInner.height / 100) + magnifiedImg.offsetHeight / 2}px`;
    }

    // 绑定事件
    productMainImg.addEventListener('mouseenter', () => {
        initLens();
        lens.style.display = 'block';
        magnifiedImg.style.display = 'block';
    });

    productMainImg.addEventListener('mouseleave', () => {
        lens.style.display = 'none';
        magnifiedImg.style.display = 'none';
    });

    productMainImg.addEventListener('mousemove', updateMagnifier);

    // 切换缩略图
    const thumbnails = $$('.product-thumbnail');
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', () => {
            const thumbnailImg = thumbnail.querySelector('img');
            img.src = thumbnailImg.src;
            magnifiedImgInner.src = thumbnailImg.src;

            // 更新缩略图选中状态
            thumbnails.forEach(item => item.classList.remove('active'));
            thumbnail.classList.add('active');
        });
    });
}

// 购物车功能
function initCart() {
    const addToCartBtns = $$('.add-to-cart, .add-to-cart-detail');
    const cartCount = $('.cart-count');

    // 添加商品到购物车
    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 获取商品信息
            const productCard = this.closest('.product-card') || this.closest('.product-detail');
            const quantity = productCard ? parseInt(productCard.querySelector('.quantity-input')?.value || 1) : 1;

            // 在实际项目中，这里应该发送请求到服务器添加商品
            // 这里仅做前端模拟
            const currentCount = parseInt(cartCount.textContent || '0');
            cartCount.textContent = currentCount + quantity;

            // 显示成功提示，2秒后自动消失
            const successMsg = document.createElement('div');
            successMsg.className = 'cart-success-message';
            successMsg.textContent = `已成功添加${quantity}件商品到购物车！`;
            document.body.appendChild(successMsg);

            // 添加动画效果
            setTimeout(() => {
                successMsg.classList.add('show');
            }, 10);

            // 2秒后移除提示
            setTimeout(() => {
                successMsg.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(successMsg);
                }, 300);
            }, 2000);
        });
    });

    // 初始化快速添加功能
    initQuickAdd();
}

// 快速添加商品功能
function initQuickAdd() {
    // 为所有商品卡片添加数量选择器
    const productCards = $$('.product-card');

    productCards.forEach(card => {
        // 检查是否已经有数量选择器
        if (card.querySelector('.quantity-selector')) return;

        // 创建数量选择器
        const quantitySelector = document.createElement('div');
        quantitySelector.className = 'quantity-selector';
        quantitySelector.innerHTML = `
            <button type="button" class="quantity-btn decrease">-</button>
            <input type="number" class="quantity-input" value="1" min="1" max="99">
            <button type="button" class="quantity-btn increase">+</button>
        `;

        // 将数量选择器添加到商品卡片
        const addToCartBtn = card.querySelector('.add-to-cart');
        if (addToCartBtn) {
            addToCartBtn.parentNode.insertBefore(quantitySelector, addToCartBtn);
        }

        // 绑定数量选择器事件
        const decreaseBtn = quantitySelector.querySelector('.decrease');
        const increaseBtn = quantitySelector.querySelector('.increase');
        const quantityInput = quantitySelector.querySelector('.quantity-input');

        decreaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });

        increaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            if (value < 99) {
                quantityInput.value = value + 1;
            }
        });

        // 防止手动输入非法值
        quantityInput.addEventListener('change', () => {
            let value = parseInt(quantityInput.value);
            if (isNaN(value) || value < 1) {
                quantityInput.value = 1;
            } else if (value > 99) {
                quantityInput.value = 99;
            }
        });
    });
}

    // 购物车页面功能
    const cartItems = $$('.cart-item');
    const cartCheckboxes = $$('.cart-checkbox input[type="checkbox"]');
    const selectAllCheckbox = $('.cart-select-all input[type="checkbox"]');
    const cartTotalPrice = $('.cart-total-price');

    if (cartItems.length > 0) {
        // 更新购物车总价
        function updateCartTotal() {
            let total = 0;
            cartItems.forEach((item, index) => {
                if (cartCheckboxes[index].checked) {
                    const price = parseFloat(item.querySelector('.cart-product-price').textContent.replace('¥', ''));
                    const quantity = parseInt(item.querySelector('.cart-quantity input').value);
                    total += price * quantity;
                }
            });
            cartTotalPrice.textContent = `¥${total.toFixed(2)}`;
        }

        // 绑定复选框事件
        cartCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateCartTotal);
        });

        // 全选/取消全选
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                cartCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateCartTotal();
            });
        }

        // 更新商品数量
        const quantityBtns = $$('.cart-quantity-btn');
        quantityBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const input = this.parentElement.querySelector('input');
                let value = parseInt(input.value);

                if (this.textContent === '+') {
                    value++;
                } else if (this.textContent === '-' && value > 1) {
                    value--;
                }

                input.value = value;

                // 更新小计
                const item = this.closest('.cart-item');
                const price = parseFloat(item.querySelector('.cart-product-price').textContent.replace('¥', ''));
                const subtotal = price * value;
                item.querySelector('.cart-product-subtotal').textContent = `¥${subtotal.toFixed(2)}`;

                updateCartTotal();
            });
        });

        // 删除商品
        const removeLinks = $$('.cart-remove');
        removeLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (confirm('确定要删除该商品吗？')) {
                    const item = this.closest('.cart-item');
                    item.remove();
                    updateCartTotal();

                    // 如果购物车为空，显示空购物车提示
                    if ($$('.cart-item').length === 0) {
                        const cartContainer = $('.cart-container');
                        cartContainer.innerHTML = `
                            <div class="empty-cart">
                                <i class="fa fa-shopping-cart"></i>
                                <p>购物车空空如也，去挑选心仪的商品吧！</p>
                                <a href="index.html" class="continue-shopping">继续购物</a>
                            </div>
                        `;
                    }
                }
            });
        });

        // 初始化总价
        updateCartTotal();
    }


// 默认账户信息
const defaultAccount = {
    username: "admin",
    password: "admin_123_",
    phone: "***********",
    email: "<EMAIL>"
};

// 快速填充登录表单
function fillLoginForm() {
    const loginForm = $('#login-form');
    if (!loginForm) return;

    const usernameInput = loginForm.querySelector('input[name="username"]');
    const passwordInput = loginForm.querySelector('input[name="password"]');

    if (usernameInput) {
        usernameInput.value = defaultAccount.username;
        clearError(usernameInput);
    }

    if (passwordInput) {
        passwordInput.value = defaultAccount.password;
        clearError(passwordInput);
    }

    // 显示提示信息
    alert('默认账户信息：\n用户名：' + defaultAccount.username + '\n密码：' + defaultAccount.password + '\n手机号：' + defaultAccount.phone);
}

// 快速填充注册表单
function fillRegisterForm() {
    const registerForm = $('#register-form');
    if (!registerForm) return;

    const usernameInput = registerForm.querySelector('input[name="username"]');
    const passwordInput = registerForm.querySelector('input[name="password"]');
    const confirmPasswordInput = registerForm.querySelector('input[name="confirm_password"]');
    const phoneInput = registerForm.querySelector('input[name="phone"]');
    const emailInput = registerForm.querySelector('input[name="email"]');

    if (usernameInput) {
        usernameInput.value = defaultAccount.username;
        clearError(usernameInput);
    }

    if (passwordInput) {
        passwordInput.value = defaultAccount.password;
        clearError(passwordInput);
    }

    if (confirmPasswordInput) {
        confirmPasswordInput.value = defaultAccount.password;
        clearError(confirmPasswordInput);
    }

    if (phoneInput) {
        phoneInput.value = defaultAccount.phone;
        clearError(phoneInput);
    }

    if (emailInput) {
        emailInput.value = defaultAccount.email;
        clearError(emailInput);
    }

    // 显示提示信息
    alert('默认账户信息：\n用户名：' + defaultAccount.username + '\n密码：' + defaultAccount.password + '\n手机号：' + defaultAccount.phone);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表单验证
    validateForm('#login-form');
    validateForm('#register-form');

    // 初始化实时表单验证
    addLiveValidation('#login-form');
    addLiveValidation('#register-form');

    // 初始化轮播图
    initCarousel();

    // 初始化放大镜
    initMagnifier();

    // 初始化购物车
    initCart();

    // 初始化快速登录按钮
    const quickLoginBtn = $('#quick-login-btn');
    if (quickLoginBtn) {
        quickLoginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            fillLoginForm();
        });
    }

    // 初始化快速注册按钮
    const quickRegisterBtn = $('#quick-register-btn');
    if (quickRegisterBtn) {
        quickRegisterBtn.addEventListener('click', function(e) {
            e.preventDefault();
            fillRegisterForm();
        });
    }
});
