/* 用户中心样式 - 浅色调主题 */
:root {
    --primary-color: #78a4d4;
    --primary-light: #a9c7e7;
    --primary-dark: #5a87b5;
    --accent-color: #f5b041;
    --text-color: #4a4a4a;
    --text-light: #7a7a7a;
    --bg-color: #f9f9f9;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --success-color: #66bb6a;
    --info-color: #42a5f5;
    --warning-color: #ffa726;
    --danger-color: #ef5350;
}

/* 用户中心容器 */
.user-center-container {
    display: flex;
    margin: 30px 0;
    min-height: 600px;
    background-color: var(--bg-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    animation: fadeIn 0.5s ease-in-out;
}

/* 侧边栏 */
.user-sidebar {
    width: 250px;
    background-color: var(--card-bg);
    border-right: 1px solid var(--border-color);
    padding: 30px 0;
}

.user-info {
    text-align: center;
    padding: 0 20px 20px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.user-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid var(--primary-light);
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 5px;
}

.user-level {
    font-size: 14px;
    color: var(--primary-color);
    background-color: rgba(120, 164, 212, 0.1);
    padding: 3px 10px;
    border-radius: 15px;
    display: inline-block;
}

.sidebar-menu {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 2px;
}

.sidebar-menu li a {
    display: block;
    padding: 12px 25px;
    color: var(--text-color);
    transition: all 0.3s ease;
    position: relative;
    font-size: 15px;
}

.sidebar-menu li a:hover {
    background-color: rgba(120, 164, 212, 0.1);
    color: var(--primary-color);
    padding-left: 30px;
}

.sidebar-menu li.active a {
    background-color: rgba(120, 164, 212, 0.15);
    color: var(--primary-color);
    font-weight: bold;
    border-left: 4px solid var(--primary-color);
}

/* 内容区域 */
.user-content {
    flex: 1;
    padding: 30px;
    background-color: var(--card-bg);
}

.content-section {
    animation: fadeIn 0.4s ease-in-out;
}

.content-section h2 {
    font-size: 22px;
    color: var(--text-color);
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.content-section h2::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
}

/* 个人资料 */
.profile-info {
    background-color: var(--bg-color);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--border-color);
}

.info-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.info-item label {
    width: 100px;
    font-weight: bold;
    color: var(--text-light);
}

.info-item span {
    flex: 1;
    color: var(--text-color);
}

/* 按钮样式 */
.edit-btn, .submit-btn, .add-address-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 15px;
    transition: all 0.3s ease;
}

.edit-btn:hover, .submit-btn:hover, .add-address-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 标签页 */
.order-tabs, .coupon-tabs, .message-tabs, .settings-menu {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.order-tabs a, .coupon-tabs a, .message-tabs a, .settings-menu a {
    padding: 10px 20px;
    margin-right: 5px;
    color: var(--text-color);
    position: relative;
}

.order-tabs a.active, .coupon-tabs a.active, .message-tabs a.active, .settings-menu a.active {
    color: var(--primary-color);
    font-weight: bold;
}

.order-tabs a.active::after, .coupon-tabs a.active::after, .message-tabs a.active::after, .settings-menu a.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

/* 空数据提示 */
.empty-data {
    text-align: center;
    padding: 50px 0;
    color: var(--text-light);
    font-size: 16px;
}

/* 表单样式 */
.change-password-form .form-group {
    margin-bottom: 20px;
}

.change-password-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--text-color);
}

.change-password-form input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    transition: border-color 0.3s;
}

.change-password-form input:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-center-container {
        flex-direction: column;
    }
    
    .user-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding: 20px 0;
    }
    
    .user-content {
        padding: 20px;
    }
}
