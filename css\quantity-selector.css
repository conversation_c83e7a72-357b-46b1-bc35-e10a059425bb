/* 数量选择器样式 */
.quantity-selector {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    max-width: 120px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.2s;
}

.quantity-btn:hover {
    background-color: #e0e0e0;
}

.quantity-input {
    width: 40px;
    height: 30px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 0 5px;
    padding: 0;
    font-size: 14px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .quantity-selector {
        max-width: 100px;
    }
    
    .quantity-btn {
        width: 25px;
        height: 25px;
    }
    
    .quantity-input {
        width: 35px;
        height: 25px;
    }
}