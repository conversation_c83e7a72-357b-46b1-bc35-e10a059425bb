/* 全局样式 - 浅色调主题 */
:root {
    --primary-color: #78a4d4;
    --primary-light: #a9c7e7;
    --primary-dark: #5a87b5;
    --accent-color: #f5b041;
    --text-color: #4a4a4a;
    --text-light: #7a7a7a;
    --bg-color: #f9f9f9;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --success-color: #66bb6a;
    --info-color: #42a5f5;
    --warning-color: #ffa726;
    --danger-color: #ef5350;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: background-color 0.3s ease;
}

a {
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s ease;
}

a:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.container {
    width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 头部样式 */
header {
    background-color: var(--card-bg);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.3s ease;
}

.header-top {
    background-color: var(--primary-dark);
    color: white;
    padding: 5px 0;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-top-left a {
    color: white;
    margin-right: 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.header-top-left a:hover {
    color: var(--accent-color);
    transform: translateY(-2px);
}

.header-top-right a {
    color: white;
    margin-left: 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.header-top-right a:hover {
    color: var(--accent-color);
    transform: translateY(-2px);
}

.header-main {
    padding: 15px 0;
    animation: fadeInDown 0.5s ease-in-out;
}

.header-main .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 28px;
    font-weight: bold;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
    text-shadow: 0 2px 10px rgba(120, 164, 212, 0.3);
}

.search-box {
    display: flex;
    width: 500px;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.search-box input {
    width: 80%;
    padding: 10px;
    border: 2px solid var(--primary-light);
    border-right: none;
    outline: none;
    transition: all 0.3s ease;
    border-radius: 4px 0 0 4px;
}

.search-box input:focus {
    border-color: var(--primary-color);
}

.search-box button {
    width: 20%;
    padding: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 0 4px 4px 0;
}

.search-box button:hover {
    background-color: var(--primary-dark);
}

.user-actions {
    display: flex;
    align-items: center;
}

.user-actions a {
    margin-left: 20px;
    font-size: 16px;
    position: relative;
    transition: all 0.3s ease;
}

.user-actions a:hover {
    color: var(--primary-color);
}

.cart-icon {
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

/* 导航栏样式 */
nav {
    background-color: var(--primary-color);
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.nav-list {
    display: flex;
    list-style: none;
}

.nav-list li {
    position: relative;
    transition: all 0.3s ease;
}

.nav-list li a {
    display: block;
    padding: 15px 20px;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
    position: relative;
}

.nav-list li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: var(--accent-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-list li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-list li a:hover::after {
    width: 70%;
}

.sub-nav {
    position: absolute;
    top: 100%;
    left: 0;
    width: 200px;
    background-color: var(--card-bg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 10;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s ease;
}

.nav-list li:hover .sub-nav {
    display: block;
    transform: translateY(0);
    opacity: 1;
    animation: fadeInUp 0.3s ease-in-out;
}

.sub-nav li a {
    color: var(--text-color);
    padding: 12px 15px;
    font-weight: normal;
    border-left: 3px solid transparent;
}

.sub-nav li a:hover {
    background-color: rgba(120, 164, 212, 0.1);
    color: var(--primary-color);
    border-left: 3px solid var(--primary-color);
}

.sub-nav li a::after {
    display: none;
}

/* 轮播图样式 */
.carousel {
    position: relative;
    height: 400px;
    overflow: hidden;
    margin: 20px 0;
}

.carousel-inner {
    display: flex;
    transition: transform 0.5s ease;
    height: 100%;
}

.carousel-item {
    min-width: 100%;
    height: 100%;
}

.carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-control {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    cursor: pointer;
    z-index: 5;
}

.carousel-prev {
    left: 10px;
}

.carousel-next {
    right: 10px;
}

.carousel-indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    z-index: 5;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    margin: 0 5px;
    cursor: pointer;
}

.carousel-indicator.active {
    background-color: #fff;
}

/* 商品展示样式 */
.section-title {
    text-align: center;
    margin: 30px 0;
    font-size: 28px;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #ff6700;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 40px;
}

.product-card {
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.product-img {
    height: 200px;
    overflow: hidden;
}

.product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.product-card:hover .product-img img {
    transform: scale(1.05);
}

.product-info {
    padding: 15px;
}

.product-name {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    height: 40px;
    overflow: hidden;
}

.product-price {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.current-price {
    font-size: 18px;
    font-weight: bold;
    color: #ff6700;
}

.original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
    margin-left: 10px;
}

.product-actions {
    display: flex;
    justify-content: space-between;
}

.add-to-cart {
    background-color: #ff6700;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.add-to-cart:hover {
    background-color: #e65100;
}

.view-details {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.view-details:hover {
    background-color: #e0e0e0;
}

/* 页脚样式 */
footer {
    background-color: #333;
    color: #fff;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 30px;
}

.footer-column h3 {
    font-size: 18px;
    margin-bottom: 15px;
    position: relative;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: #ff6700;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 10px;
}

.footer-column ul li a {
    color: #ccc;
}

.footer-column ul li a:hover {
    color: #ff6700;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
}

.footer-bottom p {
    font-size: 14px;
    color: #ccc;
}

/* 登录和注册页面样式 */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 200px);
    padding: 40px 0;
    animation: fadeIn 0.5s ease-in-out;
}

.auth-form {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
    padding: 35px;
    width: 400px;
    transition: all 0.3s ease;
    transform: translateY(0);
}

.auth-form:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--text-color);
    position: relative;
    padding-bottom: 10px;
}

.auth-form h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    outline: none;
    transition: all 0.3s ease;
    background-color: var(--bg-color);
    color: var(--text-color);
}

.form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(120, 164, 212, 0.2);
}

.form-group .error-message {
    color: var(--danger-color);
    font-size: 14px;
    margin-top: 5px;
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.form-group.error input {
    border-color: var(--danger-color);
}

.form-group.error .error-message {
    display: block;
}

.radio-group, .checkbox-group {
    display: flex;
    gap: 15px;
}

.radio-group label, .checkbox-group label {
    font-weight: normal;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-group input, .checkbox-group input {
    width: auto;
    margin-right: 5px;
}

.auth-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.auth-btn:active {
    transform: translateY(0);
}

.auth-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.auth-btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

.auth-links {
    text-align: center;
    margin-top: 20px;
}

.auth-links a {
    color: var(--primary-color);
    margin: 0 10px;
    transition: all 0.3s ease;
    position: relative;
}

.auth-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
}

.auth-links a:hover::after {
    width: 100%;
}

/* 登录成功消息 */
.login-success-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.15);
    padding: 20px 30px;
    display: flex;
    align-items: center;
    opacity: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.login-success-message.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.success-icon {
    width: 40px;
    height: 40px;
    background-color: var(--success-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    margin-right: 15px;
}

.success-text {
    font-size: 16px;
    color: var(--text-color);
}

/* 商品详情页样式 */
.product-detail {
    display: flex;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 30px 0;
    padding: 30px;
}

.product-gallery {
    width: 50%;
    padding-right: 30px;
}

.product-main-img {
    position: relative;
    height: 400px;
    overflow: hidden;
    margin-bottom: 20px;
}

.product-main-img img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.magnifier-lens {
    position: absolute;
    width: 150px;
    height: 150px;
    border: 2px solid #ff6700;
    background-color: rgba(255, 255, 255, 0.3);
    cursor: none;
    display: none;
}

.magnified-img {
    position: absolute;
    top: 0;
    right: -450px;
    width: 400px;
    height: 400px;
    border: 1px solid #ddd;
    overflow: hidden;
    background-color: #fff;
    display: none;
}

.magnified-img img {
    position: absolute;
}

.product-thumbnails {
    display: flex;
    gap: 10px;
}

.product-thumbnail {
    width: 80px;
    height: 80px;
    border: 1px solid #ddd;
    cursor: pointer;
}

.product-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-thumbnail.active {
    border-color: #ff6700;
}

.product-details {
    width: 50%;
}

.product-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
}

.product-price-detail {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.product-current-price {
    font-size: 28px;
    font-weight: bold;
    color: #ff6700;
}

.product-original-price {
    font-size: 18px;
    color: #999;
    text-decoration: line-through;
    margin-left: 15px;
}

.product-description {
    margin-bottom: 20px;
    line-height: 1.8;
}

.product-quantity {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.product-quantity span {
    margin-right: 15px;
    font-weight: bold;
}

.quantity-input {
    display: flex;
    align-items: center;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.quantity-input input {
    width: 50px;
    height: 30px;
    border: 1px solid #ddd;
    border-left: none;
    border-right: none;
    text-align: center;
    outline: none;
}

.product-actions-detail {
    display: flex;
    gap: 15px;
}

.add-to-cart-detail, .buy-now {
    padding: 12px 25px;
    border: none;
    border-radius: 3px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.add-to-cart-detail {
    background-color: #ff6700;
    color: #fff;
}

.add-to-cart-detail:hover {
    background-color: #e65100;
}

.buy-now {
    background-color: #ff9800;
    color: #fff;
}

.buy-now:hover {
    background-color: #f57c00;
}

/* 购物车页面样式 */
.cart-container {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 30px 0;
    padding: 30px;
}

.cart-header {
    display: grid;
    grid-template-columns: 50px 100px 2fr 1fr 1fr 1fr 100px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
}

.cart-item {
    display: grid;
    grid-template-columns: 50px 100px 2fr 1fr 1fr 1fr 100px;
    padding: 20px 0;
    border-bottom: 1px solid #ddd;
    align-items: center;
}

.cart-checkbox {
    text-align: center;
}

.cart-product-img {
    width: 80px;
    height: 80px;
}

.cart-product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-product-name {
    font-weight: bold;
}

.cart-product-price {
    color: #ff6700;
}

.cart-quantity {
    display: flex;
    align-items: center;
}

.cart-quantity-btn {
    width: 25px;
    height: 25px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.cart-quantity input {
    width: 40px;
    height: 25px;
    border: 1px solid #ddd;
    border-left: none;
    border-right: none;
    text-align: center;
    outline: none;
}

.cart-product-subtotal {
    font-weight: bold;
    color: #ff6700;
}

.cart-product-action {
    text-align: center;
}

.cart-remove {
    color: #999;
    cursor: pointer;
}

.cart-remove:hover {
    color: #f44336;
}

.cart-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.cart-select-all {
    display: flex;
    align-items: center;
}

.cart-select-all input {
    margin-right: 5px;
}

.cart-summary {
    display: flex;
    align-items: center;
}

.cart-total {
    margin-right: 20px;
}

.cart-total-price {
    font-size: 20px;
    font-weight: bold;
    color: #ff6700;
}

.checkout-btn {
    background-color: #ff6700;
    color: #fff;
    border: none;
    padding: 12px 30px;
    border-radius: 3px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.checkout-btn:hover {
    background-color: #e65100;
}

.empty-cart {
    text-align: center;
    padding: 50px 0;
}

.empty-cart i {
    font-size: 60px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-cart p {
    font-size: 18px;
    color: #999;
    margin-bottom: 20px;
}

.continue-shopping {
    background-color: #ff6700;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 3px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: inline-block;
}

.continue-shopping:hover {
    background-color: var(--primary-dark);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(120, 164, 212, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(120, 164, 212, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(120, 164, 212, 0);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 1;
    }
    20% {
        transform: scale(25, 25);
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        width: 100%;
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .product-detail {
        flex-direction: column;
    }

    .product-gallery, .product-details {
        width: 100%;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: 1fr;
    }

    .header-main .container {
        flex-direction: column;
    }

    .search-box {
        width: 100%;
        margin: 15px 0;
    }

    .nav-list {
        flex-wrap: wrap;
    }
}