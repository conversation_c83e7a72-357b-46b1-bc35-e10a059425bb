<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>放大镜功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .product-main-img {
            position: relative;
            width: 500px;
            height: 400px;
            margin: 20px auto;
            border: 1px solid #ddd;
            overflow: hidden;
        }
        
        .product-main-img img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .magnifier-lens {
            position: absolute;
            width: 150px;
            height: 150px;
            border: 2px solid #78a4d4;
            background-color: rgba(255, 255, 255, 0.3);
            cursor: none;
            display: none;
            z-index: 10;
        }
        
        .magnified-img {
            position: absolute;
            top: 0;
            right: -450px;
            width: 400px;
            height: 400px;
            border: 1px solid #ddd;
            overflow: hidden;
            background-color: #fff;
            display: none;
            z-index: 20;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .magnified-img img {
            position: absolute;
        }
        
        .thumbnails {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .thumbnail {
            width: 80px;
            height: 80px;
            border: 2px solid transparent;
            cursor: pointer;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .thumbnail.active {
            border-color: #78a4d4;
        }
        
        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .instructions {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        h1 {
            text-align: center;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>放大镜功能测试</h1>
        
        <div class="instructions">
            <p><strong>使用说明：</strong></p>
            <p>1. 将鼠标悬停在主图片上，会出现放大镜和放大显示区域</p>
            <p>2. 移动鼠标可以查看图片的不同部分</p>
            <p>3. 点击下方缩略图可以切换不同的图片</p>
            <p>4. 在移动设备上放大镜功能会自动隐藏</p>
        </div>
        
        <div class="product-main-img">
            <img id="mainImage" src="images/dzsc/sj.png" alt="测试图片">
            <div class="magnifier-lens"></div>
            <div class="magnified-img">
                <img src="images/dzsc/sj.png" alt="放大图片">
            </div>
        </div>
        
        <div class="thumbnails">
            <div class="thumbnail active" onclick="changeImage('images/dzsc/sj.png')">
                <img src="images/dzsc/sj.png" alt="手机">
            </div>
            <div class="thumbnail" onclick="changeImage('images/dzsc/dn.png')">
                <img src="images/dzsc/dn.png" alt="电脑">
            </div>
            <div class="thumbnail" onclick="changeImage('images/products/phone1.jpg')">
                <img src="images/products/phone1.jpg" alt="手机2">
            </div>
            <div class="thumbnail" onclick="changeImage('images/dzsc/mi15zm.jpg')">
                <img src="images/dzsc/mi15zm.jpg" alt="小米手机">
            </div>
        </div>
    </div>

    <script>
        // 图片切换功能
        function changeImage(src) {
            const mainImage = document.getElementById('mainImage');
            const magnifiedImg = document.querySelector('.magnified-img img');
            
            mainImage.src = src;
            magnifiedImg.src = src;
            
            // 更新缩略图选中状态
            const thumbnails = document.querySelectorAll('.thumbnail');
            thumbnails.forEach(thumb => {
                if (thumb.querySelector('img').src.includes(src.split('/').pop())) {
                    thumb.classList.add('active');
                } else {
                    thumb.classList.remove('active');
                }
            });
        }

        // 放大镜功能
        function initMagnifier() {
            const productMainImg = document.querySelector('.product-main-img');
            if (!productMainImg) return;

            const img = productMainImg.querySelector('img');
            const lens = document.querySelector('.magnifier-lens');
            const magnifiedImg = document.querySelector('.magnified-img');
            const magnifiedImgInner = magnifiedImg.querySelector('img');

            // 设置放大倍数
            const ratio = 2.5;

            // 初始化放大镜
            function initLens() {
                magnifiedImgInner.src = img.src;
                const lensWidth = magnifiedImg.offsetWidth / ratio;
                const lensHeight = magnifiedImg.offsetHeight / ratio;
                lens.style.width = `${lensWidth}px`;
                lens.style.height = `${lensHeight}px`;
            }

            // 更新放大镜位置和放大效果
            function updateMagnifier(e) {
                const rect = img.getBoundingClientRect();
                let mouseX = e.clientX - rect.left;
                let mouseY = e.clientY - rect.top;

                const lensWidth = lens.offsetWidth;
                const lensHeight = lens.offsetHeight;

                // 限制放大镜在图片内
                if (mouseX < lensWidth / 2) mouseX = lensWidth / 2;
                if (mouseX > rect.width - lensWidth / 2) mouseX = rect.width - lensWidth / 2;
                if (mouseY < lensHeight / 2) mouseY = lensHeight / 2;
                if (mouseY > rect.height - lensHeight / 2) mouseY = rect.height - lensHeight / 2;

                // 设置放大镜位置
                lens.style.left = `${mouseX - lensWidth / 2}px`;
                lens.style.top = `${mouseY - lensHeight / 2}px`;

                // 计算放大图片的位置
                const magnifyX = (mouseX / rect.width) * 100;
                const magnifyY = (mouseY / rect.height) * 100;

                magnifiedImgInner.style.width = `${img.width * ratio}px`;
                magnifiedImgInner.style.height = `${img.height * ratio}px`;
                magnifiedImgInner.style.left = `${-magnifyX * (magnifiedImgInner.width / 100) + magnifiedImg.offsetWidth / 2}px`;
                magnifiedImgInner.style.top = `${-magnifyY * (magnifiedImgInner.height / 100) + magnifiedImg.offsetHeight / 2}px`;
            }

            // 绑定事件
            productMainImg.addEventListener('mouseenter', () => {
                initLens();
                lens.style.display = 'block';
                magnifiedImg.style.display = 'block';
            });

            productMainImg.addEventListener('mouseleave', () => {
                lens.style.display = 'none';
                magnifiedImg.style.display = 'none';
            });

            productMainImg.addEventListener('mousemove', updateMagnifier);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMagnifier();
        });
    </script>
</body>
</html>
