// 用户中心功能

// 用户数据处理
function handleUserData() {
    // 从本地存储获取用户信息
    const userInfo = getUserInfo();
    
    if (!userInfo) {
        // 如果未登录，重定向到登录页面
        window.location.href = 'login.html';
        return;
    }
    
    // 更新页面上的用户信息
    updateUserInfoDisplay(userInfo);
}

// 从本地存储获取用户信息
function getUserInfo() {
    const userInfoStr = localStorage.getItem('userInfo');
    if (!userInfoStr) return null;
    
    try {
        return JSON.parse(userInfoStr);
    } catch (e) {
        console.error('解析用户信息失败', e);
        return null;
    }
}

// 更新页面上的用户信息显示
function updateUserInfoDisplay(userInfo) {
    // 更新顶部用户名显示
    const userNameDisplay = $('#user-name-display');
    if (userNameDisplay) {
        userNameDisplay.textContent = userInfo.username;
    }
    
    // 更新侧边栏用户名
    const sidebarUserName = $('#sidebar-user-name');
    if (sidebarUserName) {
        sidebarUserName.textContent = userInfo.username;
    }
    
    // 更新个人资料中的用户名
    const profileUsername = $('#profile-username');
    if (profileUsername) {
        profileUsername.textContent = userInfo.username;
    }
    
    // 更新上次登录时间
    const lastLoginTime = $('#last-login-time');
    if (lastLoginTime) {
        const now = new Date();
        const formattedDate = `${now.getFullYear()}-${padZero(now.getMonth() + 1)}-${padZero(now.getDate())} ${padZero(now.getHours())}:${padZero(now.getMinutes())}:${padZero(now.getSeconds())}`;
        lastLoginTime.textContent = formattedDate;
    }
}

// 数字补零
function padZero(num) {
    return num < 10 ? `0${num}` : num;
}

// 初始化侧边栏菜单
function initSidebarMenu() {
    const menuItems = $$('.sidebar-menu li a');
    const contentSections = $$('.content-section');
    
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 获取目标内容区域的ID
            const targetId = this.getAttribute('href').substring(1);
            
            // 更新菜单项的激活状态
            menuItems.forEach(menuItem => {
                menuItem.parentElement.classList.remove('active');
            });
            this.parentElement.classList.add('active');
            
            // 显示对应的内容区域，隐藏其他区域
            contentSections.forEach(section => {
                if (section.id === targetId) {
                    section.style.display = 'block';
                    // 添加动画效果
                    section.classList.add('animated');
                } else {
                    section.style.display = 'none';
                }
            });
        });
    });
}

// 初始化标签页
function initTabs() {
    // 订单标签页
    initTabGroup('.order-tabs a');
    
    // 优惠券标签页
    initTabGroup('.coupon-tabs a');
    
    // 消息标签页
    initTabGroup('.message-tabs a');
    
    // 设置标签页
    initTabGroup('.settings-menu a');
}

// 初始化标签页组
function initTabGroup(selector) {
    const tabs = $$(selector);
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 更新标签页的激活状态
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // 这里可以添加加载对应内容的逻辑
            // 例如通过AJAX请求获取数据等
        });
    });
}

// 初始化修改密码表单
function initChangePasswordForm() {
    const form = $('.change-password-form');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const currentPassword = $('#current-password').value;
        const newPassword = $('#new-password').value;
        const confirmNewPassword = $('#confirm-new-password').value;
        
        // 简单的表单验证
        if (!currentPassword || !newPassword || !confirmNewPassword) {
            alert('请填写所有密码字段');
            return;
        }
        
        if (newPassword !== confirmNewPassword) {
            alert('两次输入的新密码不一致');
            return;
        }
        
        // 密码强度验证
        const passwordRegex = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*_).+$/;
        if (!passwordRegex.test(newPassword)) {
            alert('新密码必须包含数字、字母和下划线');
            return;
        }
        
        // 在实际项目中，这里应该发送请求到服务器修改密码
        // 这里仅做前端模拟
        alert('密码修改成功！');
        form.reset();
    });
}

// 初始化退出登录功能
function initLogout() {
    const logoutBtn = $('#logout-btn');
    if (!logoutBtn) return;
    
    logoutBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        if (confirm('确定要退出登录吗？')) {
            // 清除本地存储中的用户信息
            localStorage.removeItem('userInfo');
            
            // 重定向到登录页面
            window.location.href = 'login.html';
        }
    });
}

// 添加页面过渡动画
function addPageTransitions() {
    // 为内容区域添加进入动画
    const contentSections = $$('.content-section');
    contentSections.forEach(section => {
        if (section.style.display !== 'none') {
            section.classList.add('animated');
        }
    });
    
    // 为用户信息添加动画
    const userInfo = $('.user-info');
    if (userInfo) {
        userInfo.classList.add('animated');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 处理用户数据
    handleUserData();
    
    // 初始化侧边栏菜单
    initSidebarMenu();
    
    // 初始化标签页
    initTabs();
    
    // 初始化修改密码表单
    initChangePasswordForm();
    
    // 初始化退出登录功能
    initLogout();
    
    // 添加页面过渡动画
    addPageTransitions();
});
