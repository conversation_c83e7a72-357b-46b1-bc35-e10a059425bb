<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leng-shopping - 电子产品</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#78a4d4', 
                        secondary: '#FFB800',
                        dark: '#333333',
                        light: '#F5F5F5',
                        gray: '#999999'
                    },
                    fontFamily: {
                        sans: ['PingFang SC', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .hover-scale {
                transition: transform 0.3s ease;
            }
            .hover-scale:hover {
                transform: scale(1.03);
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- 顶部通知栏 -->
    <div class="bg-primary/10 text-primary text-center py-1 text-sm">
        <p>限时优惠：全场电子产品满1000减100，点击查看详情 ></p>
    </div>

    <!-- 导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="text-2xl font-bold text-primary">Leng-shopping</a>
                </div>

                <!-- 搜索框 -->
                <div class="flex-1 max-w-xl mx-8">
                    <div class="relative">
                        <input type="text" placeholder="搜索电子产品..." 
                               class="w-full py-2 px-4 pr-10 rounded-full border-2 border-primary focus:outline-none focus:ring-2 focus:ring-primary/50">
                        <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white p-1.5 rounded-full">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 用户功能区 -->
                <div class="flex items-center space-x-4">
                    <a href="login.html" class="text-dark hover:text-primary transition-colors">
                        <i class="fa fa-user-o mr-1"></i> 登录
                    </a>
                    <a href="cart.html" class="text-dark hover:text-primary transition-colors relative cart-icon">
                        <i class="fa fa-shopping-cart mr-1"></i> 购物车
                        <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center cart-count">0</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- 主导航 -->
        <nav class="bg-primary text-white">
            <div class="container mx-auto px-4">
                <ul class="flex items-center">
                    <li class="relative group">
                        <a href="#" class="block py-3 px-6 bg-primary/90 font-medium">
                            <i class="fa fa-th-large mr-2"></i>全部商品分类
                        </a>
                        <div class="absolute left-0 top-full w-64 bg-white shadow-lg hidden group-hover:block z-50">
                            <ul class="py-2">
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>手机</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>笔记本电脑</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>耳机</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                                <li class="px-4 py-2 hover:bg-gray-50">
                                    <a href="#" class="text-dark flex justify-between">
                                        <span>智能手表</span>
                                        <i class="fa fa-angle-right text-gray-400"></i>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li><a href="index.html" class="block py-3 px-6 hover:bg-primary/90 transition-colors">首页</a></li>
                    <li><a href="phone.html" class="block py-3 px-6 bg-primary/90 transition-colors">电子产品</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">服装鞋帽</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">家居用品</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">美妆个护</a></li>
                    <li><a href="#" class="block py-3 px-6 hover:bg-primary/90 transition-colors">食品生鲜</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-4 py-6">
        <!-- 面包屑导航 -->
        <div class="text-sm text-gray-500 mb-6">
            <a href="index.html" class="hover:text-primary">首页</a>
            <span class="mx-2">/</span>
            <span class="text-dark">电子产品</span>
        </div>

        <!-- 促销横幅 -->
        <div class="relative rounded-lg overflow-hidden mb-8 shadow-md">
            <img src="https://picsum.photos/1200/300?random=1" alt="电子产品促销" class="w-full h-64 object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-primary/70 to-transparent flex items-center">
                <div class="px-12 text-white">
                    <h2 class="text-3xl font-bold mb-2 text-shadow">夏季数码盛宴</h2>
                    <p class="text-lg mb-4">全场电子产品低至5折，限时抢购！</p>
                    <button class="bg-white text-primary px-6 py-2 rounded-full font-medium hover:bg-gray-100 transition-colors">
                        立即抢购
                    </button>
                </div>
            </div>
        </div>

        <!-- 筛选区 -->
        <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="border-b border-gray-200 pb-4 mb-4">
                <div class="flex items-center mb-3">
                    <h3 class="font-medium text-dark w-20">分类</h3>
                    <div class="flex flex-wrap gap-4">
                        <a href="#" class="text-primary font-medium">全部</a>
                        <a href="#" class="hover:text-primary transition-colors">手机</a>
                        <a href="#" class="hover:text-primary transition-colors">笔记本电脑</a>
                        <a href="#" class="hover:text-primary transition-colors">耳机</a>
                        <a href="#" class="hover:text-primary transition-colors">智能手表</a>
                        <a href="#" class="hover:text-primary transition-colors">平板电脑</a>
                        <a href="#" class="hover:text-primary transition-colors">相机</a>
                    </div>
                </div>
            </div>

            <div class="border-b border-gray-200 pb-4 mb-4">
                <div class="flex items-center mb-3">
                    <h3 class="font-medium text-dark w-20">品牌</h3>
                    <div class="flex flex-wrap gap-4">
                        <a href="#" class="text-primary font-medium">全部</a>
                        <a href="#" class="hover:text-primary transition-colors">华为</a>
                        <a href="#" class="hover:text-primary transition-colors">苹果</a>
                        <a href="#" class="hover:text-primary transition-colors">小米</a>
                        <a href="#" class="hover:text-primary transition-colors">三星</a>
                        <a href="#" class="hover:text-primary transition-colors">OPPO</a>
                        <a href="#" class="hover:text-primary transition-colors">VIVO</a>
                    </div>
                </div>
            </div>

            <div class="border-b border-gray-200 pb-4 mb-4">
                <div class="flex items-center mb-3">
                    <h3 class="font-medium text-dark w-20">价格</h3>
                    <div class="flex flex-wrap gap-4">
                        <a href="#" class="text-primary font-medium">全部</a>
                        <a href="#" class="hover:text-primary transition-colors">0-1000元</a>
                        <a href="#" class="hover:text-primary transition-colors">1000-3000元</a>
                        <a href="#" class="hover:text-primary transition-colors">3000-5000元</a>
                        <a href="#" class="hover:text-primary transition-colors">5000-10000元</a>
                        <a href="#" class="hover:text-primary transition-colors">10000元以上</a>
                    </div>
                </div>
            </div>

            <div>
                <div class="flex items-center mb-3">
                    <h3 class="font-medium text-dark w-20">特性</h3>
                    <div class="flex flex-wrap gap-4">
                        <a href="#" class="text-primary font-medium">全部</a>
                        <a href="#" class="hover:text-primary transition-colors">5G</a>
                        <a href="#" class="hover:text-primary transition-colors">无线充电</a>
                        <a href="#" class="hover:text-primary transition-colors">高刷新率</a>
                        <a href="#" class="hover:text-primary transition-colors">防水</a>
                        <a href="#" class="hover:text-primary transition-colors">全面屏</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 排序和商品展示 -->
        <div class="flex flex-col md:flex-row gap-6">
            <!-- 左侧广告 -->
            <div class="hidden md:block w-64">
                <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <h3 class="font-medium text-dark mb-3">热销榜单</h3>
                    <div class="space-y-4">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center font-bold">1</div>
                            <div>
                                <p class="text-sm font-medium">高端智能手机</p>
                                <p class="text-primary font-bold">¥4999</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-gray-200 text-dark rounded-full flex items-center justify-center font-bold">2</div>
                            <div>
                                <p class="text-sm font-medium">无线降噪耳机</p>
                                <p class="text-primary font-bold">¥699</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-gray-200 text-dark rounded-full flex items-center justify-center font-bold">3</div>
                            <div>
                                <p class="text-sm font-medium">超薄笔记本电脑</p>
                                <p class="text-primary font-bold">¥5999</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-4">
                    <h3 class="font-medium text-dark mb-3">推荐品牌</h3>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="border border-gray-200 rounded p-2 text-center hover:border-primary transition-colors cursor-pointer">
                            <img src="images/dzsc/huawei.jpg" alt="品牌logo" class="w-full h-10 object-contain">
                        </div>
                        <div class="border border-gray-200 rounded p-2 text-center hover:border-primary transition-colors cursor-pointer">
                            <img src="images/dzsc/mi.jpg" alt="品牌logo" class="w-full h-10 object-contain">
                        </div>
                        <div class="border border-gray-200 rounded p-2 text-center hover:border-primary transition-colors cursor-pointer">
                            <img src="images/dzsc/iPhone.png" alt="品牌logo" class="w-full h-10 object-contain">
                        </div>
                        <div class="border border-gray-200 rounded p-2 text-center hover:border-primary transition-colors cursor-pointer">
                            <img src="images/dzsc/Samsung.jpg" alt="品牌logo" class="w-full h-10 object-contain">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧商品列表 -->
            <div class="flex-1">
                <!-- 排序工具栏 -->
                <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <div class="flex items-center gap-1">
                            <button class="px-4 py-2 bg-primary text-white rounded-l-md">综合</button>
                            <button class="px-4 py-2 border-t border-b border-gray-200 hover:bg-gray-50">销量</button>
                            <button class="px-4 py-2 border-t border-b border-r border-gray-200 rounded-r-md hover:bg-gray-50">价格</button>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="text-sm text-gray-500">共找到 128 件商品</span>
                            <div class="flex items-center gap-1 border border-gray-200 rounded px-2 py-1">
                                <span class="text-sm">1</span>
                                <span class="text-gray-400">/</span>
                                <span class="text-sm">13</span>
                                <button class="text-gray-400 hover:text-dark">
                                    <i class="fa fa-angle-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品网格 -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <!-- 商品卡片1 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/dzsc/sj.png" alt="5G智能手机" class="w-full h-56 object-cover">
                            <div class="absolute top-2 left-2">
                                <span class="bg-primary text-white text-xs px-2 py-1 rounded">热卖</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-secondary px-1 rounded">新品</span>
                                <span class="text-xs text-white bg-primary px-1 rounded">5G</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">2025新款5G智能手机 12GB+256GB 高清拍照 超长待机</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥3999</span>
                                <span class="text-gray-400 line-through text-sm">¥4999</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-half-o"></i>
                                    <span class="text-gray-500 ml-1">4.8 (128)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 2.5万+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                   <a href="phone.html" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商品卡片2 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/dzsc/dn.png" alt="轻薄笔记本电脑" class="w-full h-56 object-cover">
                            <div class="absolute top-2 left-2">
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded">促销</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-blue-500 px-1 rounded">商务</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">14英寸轻薄笔记本电脑 全金属机身 酷睿i5 16GB+512GB 超长续航</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥5999</span>
                                <span class="text-gray-400 line-through text-sm">¥6999</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-o"></i>
                                    <span class="text-gray-500 ml-1">4.0 (86)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 1.2万+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                    <a href="diannao.html" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商品卡片3 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/products/headphone1.jpg" alt="无线降噪耳机" class="w-full h-56 object-cover">
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-purple-500 px-1 rounded">爆款</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">主动降噪无线蓝牙耳机 高清通话 防水防尘 30小时续航</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥699</span>
                                <span class="text-gray-400 line-through text-sm">¥799</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <span class="text-gray-500 ml-1">4.9 (324)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 5.6万+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                    <a href="#" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商品卡片4 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/products/watch1.jpg" alt="智能手表" class="w-full h-56 object-cover">
                            <div class="absolute top-2 left-2">
                                <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded">限时</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-teal-500 px-1 rounded">运动</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">多功能智能手表 血氧监测 心率检测 睡眠监测 50米防水</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥999</span>
                                <span class="text-gray-400 line-through text-sm">¥1299</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-half-o"></i>
                                    <span class="text-gray-500 ml-1">4.7 (156)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 3.1万+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                   <a href="#" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商品卡片5 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/dzsc/pbdn.jpg" alt="平板电脑" class="w-full h-56 object-cover">
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-indigo-500 px-1 rounded">教育</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">10.9英寸平板电脑 全面屏 高性能处理器 128GB 网课学习</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥2999</span>
                                <span class="text-gray-400 line-through text-sm">¥3299</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-o"></i>
                                    <span class="text-gray-500 ml-1">4.2 (98)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 8600+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                    <a href="#" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商品卡片6 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/dzsc/yxsb.jpg" alt="游戏手柄" class="w-full h-56 object-cover">
                            <div class="absolute top-2 left-2">
                                <span class="bg-red-500 text-white text-xs px-2 py-1 rounded">新品</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-pink-500 px-1 rounded">游戏</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">无线游戏手柄 蓝牙5.0 震动反馈 可充电 适配多平台</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥299</span>
                                <span class="text-gray-400 line-through text-sm">¥349</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-half-o"></i>
                                    <span class="text-gray-500 ml-1">4.6 (78)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 5200+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                    <a href="#" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商品卡片7 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/dzsc/yinxiang.jpg" alt="智能音箱" class="w-full h-56 object-cover">
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-emerald-500 px-1 rounded">智能家居</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">AI智能音箱 语音助手 支持智能家居控制 高清音质</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥199</span>
                                <span class="text-gray-400 line-through text-sm">¥249</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-o"></i>
                                    <i class="fa fa-star-o"></i>
                                    <span class="text-gray-500 ml-1">3.2 (45)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 3800+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                    <a href="#" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 商品卡片8 -->
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                        <div class="relative">
                            <img src="images/dzsc/cdb.png" alt="移动电源" class="w-full h-56 object-cover">
                            <div class="absolute top-2 left-2">
                                <span class="bg-amber-500 text-white text-xs px-2 py-1 rounded">特惠</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center gap-1 mb-2">
                                <span class="text-xs text-white bg-sky-500 px-1 rounded">便携</span>
                            </div>
                            <h3 class="font-medium text-dark mb-2 line-clamp-2">20000mAh大容量移动电源 双向快充 数显电量 小巧便携</h3>
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-primary text-xl font-bold">¥149</span>
                                <span class="text-gray-400 line-through text-sm">¥199</span>
                            </div>
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center text-yellow-400 text-xs">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-o"></i>
                                    <span class="text-gray-500 ml-1">4.1 (132)</span>
                                </div>
                                <span class="text-xs text-gray-500">月销 1.8万+</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="flex-1 py-2 bg-primary/10 text-primary rounded border border-primary/20 hover:bg-primary/20 transition-colors add-to-cart">
                                    <i class="fa fa-shopping-cart mr-1"></i> 加入购物车
                                </button>
                                <button class="w-10 h-10 flex items-center justify-center border border-gray-200 rounded hover:border-primary hover:text-primary transition-colors">
                                    <a href="#" class="view-details">查看详情</a>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="mt-8 flex justify-center">
                    <div class="inline-flex rounded-md shadow-sm">
                        <a href="#" class="px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">上一页</a>
                        <a href="#" class="px-4 py-2 border-t border-b border-gray-300 bg-primary text-sm font-medium text-white">1</a>
                        <a href="#" class="px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</a>
                        <a href="#" class="px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</a>
                        <span class="px-4 py-2 border-t border-b border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                        <a href="#" class="px-4 py-2 border-t border-b border-r border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">13</a>
                        <a href="#" class="px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">下一页</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推荐商品 -->
        <div class="mt-12">
            <h2 class="text-xl font-bold text-dark mb-6">猜你喜欢</h2>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <!-- 推荐商品1 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="https://picsum.photos/200/200?random=20" alt="充电线" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">USB-C快充数据线 100W 编织防缠绕 2米长</h3>
                        <p class="text-primary font-bold">¥29.9</p>
                    </div>
                </div>

                <!-- 推荐商品2-6 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="https://picsum.photos/200/200?random=21" alt="手机壳" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">透明全包防摔手机壳 镜头保护</h3>
                        <p class="text-primary font-bold">¥39.9</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="https://picsum.photos/200/200?random=22" alt="手机支架" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">多功能手机支架 可折叠 桌面通用</h3>
                        <p class="text-primary font-bold">¥45.0</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="https://picsum.photos/200/200?random=23" alt="手机膜" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">钢化玻璃膜 全屏覆盖 防指纹</h3>
                        <p class="text-primary font-bold">¥25.8</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="https://picsum.photos/200/200?random=24" alt="散热背夹" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">手机散热背夹 制冷强劲 静音设计</h3>
                        <p class="text-primary font-bold">¥79.0</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover-scale">
                    <img src="https://picsum.photos/200/200?random=25" alt="存储卡" class="w-full h-40 object-cover">
                    <div class="p-3">
                        <h3 class="font-medium text-dark text-sm mb-2 line-clamp-2">256GB TF存储卡 U3高速 读写稳定</h3>
                        <p class="text-primary font-bold">¥109.0</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-white mt-12">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-bold mb-4">冷杰电子商城</h3>
                    <p class="text-gray-400 mb-4">提供优质电子产品，让生活更智能</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-weibo"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-wechat"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fa fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">购物指南</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">新手上路</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">常见问题</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">支付方式</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">配送说明</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">售后服务</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">售后政策</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">退款说明</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">维修服务</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">联系客服</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">关于我们</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">公司简介</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">加入我们</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">联系方式</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">隐私政策</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-500 text-sm">
                <p>© 2025 冷杰电子商城 版权所有 | 营业执照 | 增值电信业务经营许可证</p>
            </div>
        </div>
    </footer>

    <!-- 购物车悬浮按钮 -->
    <a href="#" class="fixed bottom-6 right-6 bg-primary text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg hover:bg-primary/90 transition-colors">
        <i class="fa fa-shopping-cart"></i>
    </a>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="fixed bottom-6 right-24 bg-dark text-white w-12 h-12 rounded-full flex items-center justify-center shadow-lg hover:bg-dark/90 transition-colors opacity-0 invisible">
        <i class="fa fa-arrow-up"></i>
    </button>

    <script src="js/dzcp1.js"></script>
</body>
</html>